现在我有一个需求：
制造一个java的脚本文件
环境：java21

简单的介绍：文件C:\Users\<USER>\Desktop\Html目录_001\苦力活\test.txt，里面是一个表格（武器强化等级介绍），表格里面的需要被用到的列包括：（套装id，“+6”，“+7”，“+8”，“+9”，“+10”）
列与列之间使用制表符隔开了
详细需求：
1.注意：test.txt第一行不是数据行，不用输出一行字符串到C:\Users\<USER>\Desktop\Html目录_001\苦力活\result.txt，第一行以外的行，有的是纯粹的几个制表符组成的一行，也不用输出一行字符串到result.txt
2.对于 test.txt 的数据行，你都要输出一行字符串到result.txt：
示例：
test.txt里面的第二行 对应着 result.txt 里面的第一行
锁子甲	352	HP恢复速度增加10%	魔法防御力+15，物理防御力+15	吸血怒击效果+2%，准确+2	经验收益+30%，减少受到的致命伤害5%	物理致命概率+30，物理技能伤害反弹5	
352---special_enchant_desc=[强化+6\nHP恢复速度增加10%\n强化+7\n魔法防御力+15\n物理防御力+15\n强化+8\n吸血怒击效果+2%\n强化+9\n最大生命+200\n强化+10\n物理致命概率+30\n物理技能伤害反弹5\n]

3.这样的程序用java来写
实际上就是利用表格里的数据：填写到下面的框架
将{。。。}---special_enchant_desc=[强化+6\n{。。。}\n强化+7\n{。。。}\n{。。。}\n强化+8\n{。。。}\n强化+9\n{。。。}\n强化+10\n{。。。}\n{。。。}\n]
注意：
具体的 强化+...\n 这样的字符串 后面衔接几个 “{。。。}\n” 是由表格本身的 内容决定的：例如：魔法防御力+15，物理防御力+15  对应着 强化+7\n魔法防御力+15\n物理防御力+15\n

4.这个java脚本由你来完成，开始吧