<html>
<title>自动 CP/HP/MP [设置]</title>

<body>
  <br>
  <center>
    <font name=hs28>自动设置</font>
  </center><br>
  <img src="L2UI.SquareGray" width=285 height=1>
  <br>
  <center>
    <font name=hs9 color=E3C855>自动功能<br1>使用 CP/HP/MP 药剂</font>
  </center>
  <br1>
    <table cellspacing=0 cellpadding=0>
      <tr>
        <td width=20></td>
        <td width=130 align="center"></td>
        <td width=130 align="center">
          <font name=hs8 color=838383>参数设置</font>
        </td>
      </tr>
    </table>
    <table cellspacing=0 cellpadding=0>
      <tr>
        <td background="L2UI.CheckBox_checked" width=14 height=16>
          <button value=" " action="" width=15 height=14 back="" fore="">
        </td>
        <td width=6></td>
        <td width=130>
          <font name=hs12 color=E3C855> 自动-CP</font>
        </td>
        <td width=30>
          <font name=hs12 color=E3C855>
            <?user_cp_proc?>%
          </font>
        </td>
        <td width=40>
          <edit var="cp_per" type="number" min="0" max="99" value="10" width=40 height=14>
        </td>
        <td width=80><button action="bypass -h acp_cp_setting?proc=  $cp_per" value="设置" width=80 height=22
            fore="L2UI_CT1.Button_DF_Small" back="L2UI_CT1.Button_DF_Small_Down"></td>
      </tr>
      <tr>
        <td background="L2UI.CheckBox_checked" width=14 height=16>
          <button value=" " action="" width=15 height=14 back="" fore="">
        </td>
        <td width=6></td>
        <td>
          <font name=hs12 color=782736> 自动-HP</font>
        </td>
        <td>
          <font name=hs12 color=782736>
            <?user_hp_proc?>%
          </font>
        </td>
        <td>
          <edit var="hp_per" type="number" min="0" max="99" value="10" width=40 height=14>
        </td>
        <td><button action="bypass -h acp_hp_setting?proc=  $hp_per" value="设置" width=80 height=22
            fore="L2UI_CT1.Button_DF_Small" back="L2UI_CT1.Button_DF_Small_Down"></td>
      </tr>
      <tr>
        <td background="L2UI.CheckBox_checked" width=14 height=16>
          <button value=" " action="" width=15 height=14 back="" fore="">
        </td>
        <td width=6></td>
        <td>
          <font name=hs12 color=6585b6> 自动-MP</font>
        </td>
        <td>
          <font name=hs12 color=6585b6>
            <?user_mp_proc?>%
          </font>
        </td>
        <td>
          <edit var="mp_per" type="number" min="0" max="99" value="10" width=40 height=14>
        </td>
        <td><button action="bypass -h acp_mp_setting?proc=  $mp_per" value="设置" width=80 height=22
            fore="L2UI_CT1.Button_DF_Small" back="L2UI_CT1.Button_DF_Small_Down"></td>
      </tr>
    </table>

</body>

</html>