# 简单的PowerShell脚本：将a.txt文件中每行最后的数字改为原来的30%

$inputFile = "a.txt"
$outputFile = "a_modified_ps.txt"

if (Test-Path $inputFile) {
    $content = Get-Content $inputFile -Raw
    $pattern = '(\[adena\];)(\d+)(}}[;}]?)'
    
    $modifiedContent = [regex]::Replace($content, $pattern, {
        param($match)
        $prefix = $match.Groups[1].Value
        $number = [long]$match.Groups[2].Value
        $suffix = $match.Groups[3].Value
        $newNumber = [Math]::Round($number * 0.3)
        return $prefix + $newNumber + $suffix
    })
    
    $modifiedContent | Out-File -FilePath $outputFile -Encoding UTF8 -NoNewline
    Write-Host "处理完成！结果已保存到 $outputFile"
} else {
    Write-Host "文件 $inputFile 不存在！"
}
