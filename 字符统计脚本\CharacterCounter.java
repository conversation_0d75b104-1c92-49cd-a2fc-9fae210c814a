import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 字符统计工具
 * 统计指定文件的字符个数，包括换行符
 */
public class CharacterCounter {
    
    public static void main(String[] args) {
        // 如果命令行有参数，使用参数作为文件路径
        if (args.length > 0) {
            String filePath = args[0];
            countCharacters(filePath);
        } else {
            // 默认统计当前目录下的示例文件
            System.out.println("使用方法: java CharacterCounter <文件路径>");
            System.out.println("示例: java CharacterCounter ../a7_6/laohuji.htm");
            
            // 也可以在这里指定默认要统计的文件
            String defaultFile = "../a7_6/laohuji.htm";
            System.out.println("\n正在统计默认文件: " + defaultFile);
            countCharacters(defaultFile);
        }
    }
    
    /**
     * 统计文件字符数
     * @param filePath 文件路径
     */
    public static void countCharacters(String filePath) {
        try {
            Path path = Paths.get(filePath);
            
            // 检查文件是否存在
            if (!Files.exists(path)) {
                System.err.println("错误: 文件不存在 - " + filePath);
                return;
            }
            
            // 检查是否为文件
            if (!Files.isRegularFile(path)) {
                System.err.println("错误: 不是一个文件 - " + filePath);
                return;
            }
            
            // 读取文件内容
            String content = Files.readString(path, StandardCharsets.UTF_8);
            
            // 统计各种字符
            int totalChars = content.length();
            int lineBreaks = countLineBreaks(content);
            int chineseChars = countChineseCharacters(content);
            int englishChars = countEnglishCharacters(content);
            int digits = countDigits(content);
            int spaces = countSpaces(content);
            int otherChars = totalChars - chineseChars - englishChars - digits - spaces - lineBreaks;
            
            // 获取文件大小
            long fileSize = Files.size(path);
            
            // 输出统计结果
            System.out.println("=== 文件字符统计结果 ===");
            System.out.println("文件路径: " + filePath);
            System.out.println("文件大小: " + fileSize + " 字节");
            System.out.println("总字符数: " + totalChars + " 个");
            System.out.println("换行符数: " + lineBreaks + " 个");
            System.out.println("中文字符: " + chineseChars + " 个");
            System.out.println("英文字符: " + englishChars + " 个");
            System.out.println("数字字符: " + digits + " 个");
            System.out.println("空格字符: " + spaces + " 个");
            System.out.println("其他字符: " + otherChars + " 个");
            System.out.println("========================");
            
        } catch (IOException e) {
            System.err.println("读取文件时发生错误: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("发生未知错误: " + e.getMessage());
        }
    }
    
    /**
     * 统计换行符数量
     * @param content 文件内容
     * @return 换行符数量
     */
    private static int countLineBreaks(String content) {
        int count = 0;
        for (char c : content.toCharArray()) {
            if (c == '\n' || c == '\r') {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 统计中文字符数量
     * @param content 文件内容
     * @return 中文字符数量
     */
    private static int countChineseCharacters(String content) {
        int count = 0;
        for (char c : content.toCharArray()) {
            // 判断是否为中文字符
            if (c >= 0x4E00 && c <= 0x9FFF) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 统计英文字符数量
     * @param content 文件内容
     * @return 英文字符数量
     */
    private static int countEnglishCharacters(String content) {
        int count = 0;
        for (char c : content.toCharArray()) {
            if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 统计数字字符数量
     * @param content 文件内容
     * @return 数字字符数量
     */
    private static int countDigits(String content) {
        int count = 0;
        for (char c : content.toCharArray()) {
            if (c >= '0' && c <= '9') {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 统计空格字符数量
     * @param content 文件内容
     * @return 空格字符数量
     */
    private static int countSpaces(String content) {
        int count = 0;
        for (char c : content.toCharArray()) {
            if (c == ' ' || c == '\t') {
                count++;
            }
        }
        return count;
    }
}
