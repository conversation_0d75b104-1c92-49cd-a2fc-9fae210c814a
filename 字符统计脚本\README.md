# 字符统计工具

这是一个Java编写的字符统计工具，可以统计指定文件的字符个数，包括换行符。

## 功能特点

- 统计文件总字符数（包括换行符）
- 分别统计中文字符、英文字符、数字、空格、换行符等
- 显示文件大小信息
- 支持UTF-8编码的文件

## 使用方法

### 方法1：使用批处理文件（推荐）

1. 双击 `run.bat` 文件，会自动编译并运行，统计默认文件
2. 或者在命令行中运行：
   ```
   run.bat "文件路径"
   ```

### 方法2：直接使用Java命令

1. 编译Java文件：
   ```
   javac -encoding UTF-8 CharacterCounter.java
   ```

2. 运行程序：
   ```
   java CharacterCounter
   ```
   或者指定文件路径：
   ```
   java CharacterCounter "文件路径"
   ```

## 示例

统计老虎机HTML文件的字符数：
```
run.bat "../a7_6/laohuji.htm"
```

## 输出示例

```
=== 文件字符统计结果 ===
文件路径: ../a7_6/laohuji.htm
文件大小: 5234 字节
总字符数: 5234 个
换行符数: 156 个
中文字符: 45 个
英文字符: 3456 个
数字字符: 234 个
空格字符: 1234 个
其他字符: 109 个
========================
```

## 注意事项

- 确保系统已安装Java开发环境（JDK）
- 文件路径中包含空格时，请用双引号包围
- 程序使用UTF-8编码读取文件，确保文件编码正确
