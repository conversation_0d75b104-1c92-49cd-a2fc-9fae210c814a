<html noscrollbar>

<head>
  <title>特殊功能</title>
</head>

<body>
  <center>
    <img src="L2UI_CH3.herotower_deco" width=256 height=30></img>
    <table border="0" cellpadding="0" cellspacing="0" width="292" height="358">
      <tr>
        <td valign="top" align="center">
          <!-- 修改名字 -->
          <!-- 标题行 -->
          <img src="L2UI.SquareGray" width=270 height=1>
          <table width=270 bgcolor=333333>
            <tr>
              <td align=center width=270>
                <font color="LEVEL">名字更换</font>
              </td>
            </tr>
          </table>

          <img src="L2UI.SquareGray" width=270 height=1>
          <!-- 内容行 -->
          <table width="270" bgcolor="1F1818">
            <tr>
              <td width="270" valign="center" align="center">
                <table align="center">
                  <tr>
                    <td>
                      <edit var="char_name" width="150">
                    </td>
                    <td><button value="确认修改" action="bypass -h _change_char_name_normal?char_name=$char_name" width="78"
                        height="30" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                  </tr>
                </table>
              </td>
            </tr>
          </table><br>

          <img src="L2UI.SquareGray" width="270" height="1">

          <!-- 改变性别 -->
          <!-- 标题行 -->
          <img src="L2UI.SquareGray" width=270 height=1>
          <table width="270" bgcolor="333333">
            <tr>
              <td width="270" valign="center" align="center">
                <font color="LEVEL">性别更换</font>
              </td>
            </tr>
          </table>
          <img src="L2UI.SquareGray" width=270 height=1>
          <!-- 内容行 -->
          <table width="270" bgcolor="1F1818">
            <tr>
              <td width="270" valign="center" align="center">
                <button value="确认改变性别" action="bypass -h _change_char_sex?" width="150" height="20"
                  back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
              </td>
            </tr>
          </table>

          <img src="L2UI.SquareGray" width="270" height="1">

          <!-- 改变职业 -->
          <!-- 标题行 -->
          <img src="L2UI.SquareGray" width=270 height=1>
          <table width="270" bgcolor="333333">
            <tr>
              <td width="270" valign="center" align="center">
                <font color="LEVEL">职业更换</font>
              </td>
            </tr>
          </table>
          <img src="L2UI.SquareGray" width=270 height=1>
          <!-- 内容行 -->
          <table width="270" bgcolor="1F1818">
            <tr>
              <td width="270" valign="center" align="center">
                <table align="center">
                  <tr>
                    <td width="130" align="center"><button value="人类战士" action="bypass -h set_class?raceid=0&classid=0"
                        width="130" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                    <td width="130" align="center"><button value="人类法师" action="bypass -h set_class?raceid=0&classid=10"
                        width="130" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                  </tr>
                  <tr>
                    <td width="130" align="center"><button value="精灵战士" action="bypass -h set_class?raceid=1&classid=18"
                        width="130" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                    <td width="130" align="center"><button value="精灵法师" action="bypass -h set_class?raceid=1&classid=25"
                        width="130" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                  </tr>
                  <tr>
                    <td width="130" align="center"><button value="黑精灵战士"
                        action="bypass -h set_class?raceid=2&classid=31" width="130" height="18"
                        back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                    <td width="130" align="center"><button value="黑精灵法师"
                        action="bypass -h set_class?raceid=2&classid=38" width="130" height="18"
                        back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                  </tr>
                  <tr>
                    <td width="130" align="center"><button value="兽人战士" action="bypass -h set_class?raceid=4&classid=53"
                        width="130" height="18" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                    <td width="130" align="center"><button value="矮人工匠"
                        action="bypass -h set_class?raceid=5&classid=123" width="130" height="18"
                        back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <img src="L2UI.SquareGray" width="270" height="1">

          <!-- 更换盟主 -->
          <!-- 标题行 -->
          <img src="L2UI.SquareGray" width=270 height=1>
          <table width="270" bgcolor="333333">
            <tr>
              <td width="270" valign="center" align="center">
                <font color="LEVEL">盟主更换</font>
              </td>
            </tr>
          </table>
          <img src="L2UI.SquareGray" width=270 height=1>
          <!-- 内容行 -->
          <table width="270" bgcolor="1F1818">
            <tr>
              <td width="270" valign="center" align="center">
                <table align="center">
                  <tr>
                    <td>
                      <edit var="master_name" width="150">
                    </td>
                    <td><button value="确认转让" action="bypass -h _bbs_change_pledge_master?master_name=$master_name"
                        width="78" height="20" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                  </tr>
                </table>
              </td>
            </tr>
          </table><br>

          <img src="L2UI.SquareGray" width="270" height="1">

          <!-- 返回按钮 -->
          <table width="270">
            <tr>
              <td align="center">
                <button value="返回主界面" action="link test_door.htm" width="150" height="32" back="L2UI_CT1.Button_DF_Down"
                  fore="L2UI_CT1.Button_DF">
              </td>
            </tr>
          </table>

          <img src="L2UI.SquareBlank" width="290" height="1">

        </td>
      </tr>
    </table>
  </center>
</body>

</html>