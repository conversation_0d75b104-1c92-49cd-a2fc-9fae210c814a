<html>
<title>加血加蓝</title>

<body>
	<table>
		<tr>
			<td height=0></td>
		</tr>
		<tr>
			<td align="left">
				<table height=662 width=760 bgcolor="000000" background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
					<tr>
						<td>
							<table>
								<tr>
									<td align="left">
										<table width=470>
											<tr>
												<td height=6></td>
											</tr>
											<tr>
												<td align="left">
													<table width=470 height=100 bgcolor="000000" background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
														<tr>
															<td height=0></td>
														</tr>
														<tr>
															<td width=1></td>
															<td align="left">
																<table>
																	<tr>
																		<table width=760 cellspacing=0>
																			<tr>
																				<td><button value="基本设置"
																						action="bypass -h _bbs_aout_open?file=main & welfare=main" width=180
																						height=32 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
																				<td><button value="技能策略"
																						action="bypass -h _bbs_aout_open?file=fight & welfare=fight" width=180
																						height=32 back="Button_DF_Down" fore="Button_DF"></td>
																				<td><button value="打怪范围"
																						action="bypass -h _bbs_aout_open?file=path & welfare=path" width=180
																						height=32 back="Button_DF_Down" fore="Button_DF"></td>
																				<td><button value="加血加蓝"
																						action="bypass -h _bbs_aout_open?file=protect & welfare=protect" width=180
																						height=32 back="Button_DF_Down" fore="Button_DF"></td>
																			</tr>
																			<tr>
																				<td>
																					<font color="00FF00"><button value="状态魔法"
																							action="bypass -h _bbs_aout_open?file=buff & welfare=buff" width=180
																							height=32 back="Button_DF_Down" fore="Button_DF"></font>
																				</td>
																				<td>
																					<font color="FFFF00"><button value="状态道具"
																							action="bypass -h _bbs_aout_open?file=itemuse & welfare=itemuse" width=180
																							height=32 back="Button_DF_Down" fore="Button_DF"></font>
																				</td>
																				<td>
																					<font color="C0FF3E"><button value="休息设定"
																							action="bypass -h _bbs_aout_open?file=rest & welfare=rest" width=180
																							height=32 back="Button_DF_Down" fore="Button_DF"></font>
																				</td>
																				<td>
																					<font color="9A32CD"><button value="召唤设定"
																							action="bypass -h _bbs_aout_open?file=pet & welfare=pet" width=180
																							height=32 back="Button_DF_Down" fore="Button_DF"></font>
																				</td>
																			</tr>
																		</table>

																	</tr>
																	<tr>
																		<td height=26></td>
																	</tr>
																</table>
															</td>
														</tr>
													</table>

													<table>
														<tr>

															<td align="left">
																<table width=120>
																	<tr>
																		<td height=6></td>
																	</tr>
																	<tr>

																		<td align="left">
																			<table width=200 height=515 bgcolor="000000"
																				background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
																				<tr>
																					<td height=16></td>
																				</tr>
																				<tr>
																					<td>

																						<template_line>
																							<table <?PAY_COLOR?> >
																								<tr>
																									<td>
																										<table>
																											<tr>
																												<td align="left" width=16>
																													<img src="<?PAY_CLASS_OIC?>" width=12 height=12>

																													<img src="<?PAY_DISPLAY?>" width=16 height=16>
																												</td>


																												<td align="left" width=150>
																													<button align="left" value="<?PAY_NAME?> - LV:<?PAY_LV?>"
																														action="bypass get_pay_setting?id=<?PAY_ID?>" width=150
																														height=32 back="L2UI_CT1.Button_DF_Down"
																														fore="L2UI_CT1.Button_DF">
																												</td>


																												<?PAY_MASTER?>

																											</tr>
																										</table>
																									</td>
																								</tr>
																							</table>
																						</template_line>
																					</td>
																				</tr>
																			</table>
																		</td>
																		<td align="left">
																			<table width=540 height=515 bgcolor="000000"
																				background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
																				<tr>
																					<td height=0></td>
																				</tr>
																				<tr>
																					<td width=1></td>
																					<td align="left">
																						<table>
																							<tr>
																								<td>
																									<table cellspacing=0 cellpadding=0 border="1">
																										<tr>
																											<td width=52>[<font color=00FF00>自</font>]<font color=FF6A6A>HP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="p1" list="80%;70%;60%;50%;40%;30%;20%" width=40>
																											</td>
																											<td width=60>
																												<font> 时, 选择</font>
																											</td>
																											<td>
																												<combobox var="po1" list="终极治愈药水" width=100>
																											</td>
																											<td width=150><button value="对自己施展"
																													action="bypass -h htmbypass_bot.configSet hppotion $p1 $po1"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																										<tr>
																											<td width=52>[<font color=00FF00>自</font>]<font color=FF6A6A>HP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="p2" list="80%;70%;60%;50%;40%;30%;20%" width=40>
																											</td>
																											<td>
																												<font> 时, 选择</font>
																											</td>
																											<td>
																												<combobox var="sk1" list="急救术" width=100>
																											</td>
																											<td><button value="对自己施展"
																													action="bypass -h htmbypass_bot.configSet sprotect $p2 $sk1"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																										<tr>
																											<td width=52>[<font color=AAAA00>队</font>]<font color=FF6A6A>HP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="p3" list="80%;70%;60%;50%;40%;30%;20%" width=40>
																											</td>
																											<td>
																												<font> 时, 选择</font>
																											</td>
																											<td>
																												<combobox var="sk2" list="急救术" width=100>
																											</td>
																											<td align="center">
																												<table>
																												<tr>
																													<td width=15 height=22>
																														对
																													</td>
																													<td>
																														<combobox var="sk2" list="张三;李四" width=75>
																													</td>
																													<td>
																														<button value="施展" action="bypass -h htmbypass_bot.configSet pprotect $p3 $sk2" width=35 height=22
																															back="Button_DF_Down" fore="Button_DF">
																													</td>
																												</tr>
																											  </table>
																										  </td>
																										</tr>
																									</table>
																									<table cellspacing=0 cellpadding=0 border="1">
																										<tr>
																											<td width=52>[<font color=AAAA00>群</font>]<font color=FF6A6A>HP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="p5" list="80%;70%;60%;50%;40%;30%;20%" width=40>
																											</td>
																											<td width=70>
																												<font>且人数大于</font>
																											</td>
																											<td>
																												<combobox var="count" list="2人;3人;4人;5人;6人" width=40>
																											</td>
																											<td width=60>
																												<font> 时, 选择</font>
																											</td>
																											<td>
																												<combobox var="sk4" list="%skillsGroup%" width=75>
																											</td>
																											<td><button value="对队友施展"
																													action="bypass -h htmbypass_bot.configSet gprotect $count $p5 $sk4"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																										<tr>
																											<td width=52>[<font color=AAAA00>群</font>]<font color=FF6A6A>HP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="p6" list="80%;70%;60%;50%;40%;30%;20%" width=40>
																											</td>
																											<td>
																												<font>且人数大于</font>
																											</td>
																											<td>
																												<combobox var="count2" list="1人;2人;3人;4人;5人" width=40>
																											</td>
																											<td>
																												<font> 时, 选择</font>
																											</td>
																											<td align="center">生命均衡</td>
																											<td><button value="对队友施展"
																													action="bypass -h htmbypass_bot.configSet balance $count2 $p6"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																									</table>
																									<table cellspacing=0 cellpadding=0 border="1">
																										<!-- 1 -->
																										<tr>
																											<td width=52>[<font color=00FF00>自</font>]<font color=7EC0EE>MP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="m1" list="80%;70%;60%;50%;40%;30%;20%" width=40>
																											</td>
																											<td width=60>
																												<font> 时, 选择</font>
																											</td>
																											<td><combobox var="po2" list="魔力恢复剂;新手MP恢复剂;勇士MP恢复剂" width=140></td>
																											<td width=70 align="center"><button value="使用"
																													action="bypass -h htmbypass_bot.configSet mppotion $m1 $po2"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																										<tr>
																											<td width=52>[<font color=00FF00>自</font>]<font color=7EC0EE>MP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="m4" list="30%;20%;10%" width=40>
																											</td>
																											<td><font> 时, 选择</font></td>
																											<td>伊娃祝福</td>
																											<td align="center"><button value="使用"
																													action="bypass -h htmbypass_bot.configSet evaPercent $m4"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																										<tr>
																											<td width=52>[<font color=AAAA00>队</font>]<font color=7EC0EE>MP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="m2" list="80%;70%;60%;50%;40%;30%;20%" width=40>
																											</td>
																											<td>
																												<font> 时, 选择</font>
																											</td>
																											<td>
																												<combobox var="pm" list="%members%" width=140>
																											</td>
																											<td align="center"><button value="施展回复术"
																													action="bypass -h htmbypass_bot.configSet mprotect $m2 $pm"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																										<tr>
																											<td width=52>[<font color=AAAA00>自</font>]<font color=7EC0EE>MP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="m3" list="45%;35%;25%;15%;0%" width=40>
																											</td>
																											<td>
																												<font> 时, 选择</font>
																											</td>
																											<td>[%keepMp%]时<font color=FF0000>停止</font>回复术</td>
																											<td align="center"><button value="MP保护"
																													action="bypass -h htmbypass_bot.configSet keepMp $m3" width=70
																													height=22 back="Button_DF_Down" fore="Button_DF"></td>
																										</tr>
																										<!-- 5 -->
																										<tr>
																											<td width=52>[<font color=AAAA00>宠</font>]<font color=FF6A6A>HP</font><font name="hs12">&lt;</font></td>
																											<td>
																												<combobox var="p4" list="80%;70%;60%;50%;40%;30%;20%" width=40>
																											</td>
																											<td>
																												<font> 时, 选择</font>
																											</td>
																											<td>
																												<combobox var="sk3" list="%skills%" width=140>
																											</td>
																											<td align="center"><button value="对宠物施展"
																													action="bypass -h htmbypass_bot.configSet petprotect $p4 $sk3"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																									</table>
																									<table width=290>
																										<tr></tr>
																										<tr>
																											<td>HP小于:</td>
																											<td>
																												<combobox var="limit1" list="50;40;30;20" width=40>
																											</td>
																											<td>使用极限防御</td>
																											<td><button value="设置"
																													action="bypass -h htmbypass_bot.configSet limitDefense $limit1"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																										<tr>
																											<td>HP小于:</td>
																											<td>
																												<combobox var="limit2" list="50;40;30;20" width=40>
																											</td>
																											<td>使用极限闪避</td>
																											<td><button value="设置"
																													action="bypass -h htmbypass_bot.configSet limitEvade $limit2"
																													width=70 height=22 back="Button_DF_Down" fore="Button_DF">
																											</td>
																										</tr>
																									</table>
																									<table width=490 align="center">
																										<tr>
																											<td align="center"><img src="L2UI_CH3.herotower_deco" width=256
																													height=33></img></td>
																										</tr>
																									</table>
																									<table width=490 align="center" align="center">
																										<tr>
																											<td align="center"><button value="更新设置"
																													action="bypass -h htmbypass_bot.page main" width=250 height=32
																													back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
																										</tr>
																									</table>
																									<table width=490 align="center">
																										<tr>
																											<td align="center"><img src="L2UI_CH3.herotower_deco" width=256
																													height=33></img></td>
																										</tr>
																									</table>
																								</td>
																							</tr>
																							<tr>
																								<td height=26></td>
																							</tr>
																						</table>
																					</td>
																				</tr>
																			</table>
																			<table>
																				<tr>
																					<td height=4></td>
																				</tr>
																			</table>
																		</td>
																	</tr>
																</table>
															</td>
														</tr>
													</table>





</body>

</html>