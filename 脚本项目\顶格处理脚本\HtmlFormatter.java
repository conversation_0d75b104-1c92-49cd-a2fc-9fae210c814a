import java.io.*;
import java.net.URI;
import java.nio.file.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * HTML文件顶格处理脚本
 * 功能：
 * 1. 删除每行开头的空格和制表符
 * 2. 删除纯空行
 * 3. 在原文件基础上修改
 *
 * 使用方式：
 * 1. java HtmlFormatter                    - 处理脚本所在目录下唯一的htm文件
 * 2. java HtmlFormatter <目录路径>         - 处理指定目录下唯一的htm文件
 * 3. java HtmlFormatter <文件路径>         - 直接处理指定的htm文件
 */
public class HtmlFormatter {
    
    public static void main(String[] args) {
        try {
            Path targetFile = null;

            if (args.length > 0) {
                Path argPath = Paths.get(args[0]);

                // 检查参数是文件还是目录
                if (Files.isRegularFile(argPath)) {
                    // 如果是文件，直接使用
                    if (argPath.toString().toLowerCase().endsWith(".htm") ||
                        argPath.toString().toLowerCase().endsWith(".html")) {
                        targetFile = argPath;
                        System.out.println("直接处理指定文件: " + targetFile.toAbsolutePath());
                    } else {
                        System.err.println("错误: 指定的文件不是htm/html文件!");
                        System.exit(1);
                    }
                } else if (Files.isDirectory(argPath)) {
                    // 如果是目录，搜索其中的htm文件
                    System.out.println("正在搜索目录: " + argPath.toAbsolutePath());
                    List<Path> htmFiles = findHtmFiles(argPath);

                    // 验证htm文件数量
                    if (htmFiles.isEmpty()) {
                        System.err.println("错误: 在目录中未找到任何htm文件!");
                        System.exit(1);
                    }

                    if (htmFiles.size() > 1) {
                        System.err.println("错误: 在目录中找到多个htm文件，应该只有一个!");
                        System.err.println("找到的文件:");
                        for (Path file : htmFiles) {
                            System.err.println("  - " + file.getFileName());
                        }
                        System.exit(1);
                    }

                    targetFile = htmFiles.get(0);
                    System.out.println("找到目标文件: " + targetFile.getFileName());
                } else {
                    System.err.println("错误: 指定的路径不存在或无法访问: " + argPath);
                    System.exit(1);
                }
            } else {
                // 没有参数，使用脚本所在目录
                Path currentDir = getScriptDirectory();
                System.out.println("正在搜索脚本目录: " + currentDir.toAbsolutePath());

                List<Path> htmFiles = findHtmFiles(currentDir);

                // 验证htm文件数量
                if (htmFiles.isEmpty()) {
                    System.err.println("错误: 在脚本目录中未找到任何htm文件!");
                    System.exit(1);
                }

                if (htmFiles.size() > 1) {
                    System.err.println("错误: 在脚本目录中找到多个htm文件，应该只有一个!");
                    System.err.println("找到的文件:");
                    for (Path file : htmFiles) {
                        System.err.println("  - " + file.getFileName());
                    }
                    System.exit(1);
                }

                targetFile = htmFiles.get(0);
                System.out.println("找到目标文件: " + targetFile.getFileName());
            }

            processHtmlFile(targetFile);
            
            System.out.println("处理完成!");
            
        } catch (Exception e) {
            System.err.println("处理过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    /**
     * 获取脚本所在目录
     */
    private static Path getScriptDirectory() {
        try {
            // 获取当前类的位置
            String classPath = HtmlFormatter.class.getProtectionDomain()
                    .getCodeSource().getLocation().toURI().getPath();

            // 如果是.class文件，获取其父目录
            Path scriptPath = Paths.get(classPath);
            if (Files.isRegularFile(scriptPath)) {
                return scriptPath.getParent();
            } else {
                return scriptPath;
            }
        } catch (Exception e) {
            // 如果获取失败，使用当前工作目录
            System.out.println("警告: 无法获取脚本目录，使用当前工作目录");
            return Paths.get(".");
        }
    }

    /**
     * 搜索指定目录下的所有htm文件
     */
    private static List<Path> findHtmFiles(Path directory) throws IOException {
        if (!Files.exists(directory) || !Files.isDirectory(directory)) {
            throw new IOException("指定的路径不存在或不是目录: " + directory);
        }
        
        return Files.list(directory)
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().toLowerCase().endsWith(".htm") || 
                               path.toString().toLowerCase().endsWith(".html"))
                .collect(Collectors.toList());
    }
    
    /**
     * 处理HTML文件
     */
    private static void processHtmlFile(Path filePath) throws IOException {
        System.out.println("开始处理文件: " + filePath.getFileName());
        
        // 读取所有行
        List<String> lines = Files.readAllLines(filePath);
        List<String> processedLines = new ArrayList<>();
        
        int originalLineCount = lines.size();
        int emptyLinesRemoved = 0;
        
        // 逐行处理
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            String processedLine = processLine(line);
            
            // 如果处理后的行不是空行，则添加到结果中
            if (!processedLine.isEmpty()) {
                processedLines.add(processedLine);
            } else {
                emptyLinesRemoved++;
                System.out.println("删除空行 (第" + (i + 1) + "行): \"" + line + "\"");
            }
        }
        
        // 写回文件
        Files.write(filePath, processedLines);
        
        // 输出处理统计
        System.out.println("处理统计:");
        System.out.println("  原始行数: " + originalLineCount);
        System.out.println("  删除空行: " + emptyLinesRemoved);
        System.out.println("  最终行数: " + processedLines.size());
    }
    
    /**
     * 处理单行文本
     * 删除第一个非空格且非制表符字符之前的所有空格和制表符
     */
    private static String processLine(String line) {
        if (line == null) {
            return "";
        }
        
        // 检查是否为纯空行（只包含空格和制表符）
        if (line.trim().isEmpty()) {
            return "";
        }
        
        // 找到第一个非空格且非制表符的字符位置
        int firstNonWhitespaceIndex = 0;
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            if (c != ' ' && c != '\t') {
                firstNonWhitespaceIndex = i;
                break;
            }
        }
        
        // 返回从第一个非空白字符开始的子字符串
        return line.substring(firstNonWhitespaceIndex);
    }
}
