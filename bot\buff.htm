<html>
<title>状态魔法</title>

<body>
  <table>
    <tr>
      <td height=0></td>
    </tr>
    <tr>
      <td align="left">

        <table height=662 width=760 bgcolor="000000" background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
          <tr>
            <td>

              <table>
                <tr>+*
                  <td align="left">
                    <table width=470>
                      <tr>
                        <td height=6></td>
                      </tr>
                      <tr>
                        <td align="left">
                          <table width=470 height=100 bgcolor="000000" background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
                            <tr>
                              <td height=0></td>
                            </tr>
                            <tr>
                              <td width=1></td>
                              <td align="left">
                                <table>
                                  <tr>

                                    <table width=760 cellspacing=0>
                                      <tr>
                                        <td><button value="基本设置"
                                            action="bypass -h _bbs_aout_open?file=main & welfare=main" width=180
                                            height=32 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                                        <td><button value="技能策略"
                                            action="bypass -h _bbs_aout_open?file=fight & welfare=fight" width=180
                                            height=32 back="Button_DF_Down" fore="Button_DF"></td>
                                        <td><button value="打怪范围"
                                            action="bypass -h _bbs_aout_open?file=path & welfare=path" width=180
                                            height=32 back="Button_DF_Down" fore="Button_DF"></td>
                                        <td><button value="加血加蓝"
                                            action="bypass -h _bbs_aout_open?file=protect & welfare=protect" width=180
                                            height=32 back="Button_DF_Down" fore="Button_DF"></td>
                                      </tr>
                                      <tr>
                                        <td>
                                          <font color="00FF00"><button value="状态魔法"
                                              action="bypass -h _bbs_aout_open?file=buff & welfare=buff" width=180
                                              height=32 back="Button_DF_Down" fore="Button_DF"></font>
                                        </td>
                                        <td>
                                          <font color="FFFF00"><button value="状态道具"
                                              action="bypass -h _bbs_aout_open?file=itemuse & welfare=itemuse" width=180
                                              height=32 back="Button_DF_Down" fore="Button_DF"></font>
                                        </td>
                                        <td>
                                          <font color="C0FF3E"><button value="休息设定"
                                              action="bypass -h _bbs_aout_open?file=rest & welfare=rest" width=180
                                              height=32 back="Button_DF_Down" fore="Button_DF"></font>
                                        </td>
                                        <td>
                                          <font color="9A32CD"><button value="召唤设定"
                                              action="bypass -h _bbs_aout_open?file=pet & welfare=pet" width=180
                                              height=32 back="Button_DF_Down" fore="Button_DF"></font>
                                        </td>
                                      </tr>
                                    </table>

                                  </tr>
                                  <tr>
                                    <td height=26></td>
                                  </tr>
                                </table>
                              </td>
                            </tr>
                          </table>

                          <table>
                            <tr>

                              <td align="left">
                                <table width=120>
                                  <tr>
                                    <td height=6></td>
                                  </tr>
                                  <tr>

                                    <td align="left">
                                      <table width=200 height=515 bgcolor="000000"
                                        background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
                                        <tr>
                                          <td height=16></td>
                                        </tr>
                                        <tr>
                                          <td>

                                            <template_line>
                                              <table <?PAY_COLOR?> >
                                                <tr>
                                                  <td>
                                                    <table>
                                                      <tr>
                                                        <td align="left" width=16>
                                                          <img src="<?PAY_CLASS_OIC?>" width=12 height=12>

                                                          <img src="<?PAY_DISPLAY?>" width=16 height=16>
                                                        </td>


                                                        <td align="left" width=150>
                                                          <button align="left" value="<?PAY_NAME?> - LV:<?PAY_LV?>"
                                                            action="bypass get_pay_setting?id=<?PAY_ID?>" width=150
                                                            height=32 back="L2UI_CT1.Button_DF_Down"
                                                            fore="L2UI_CT1.Button_DF">
                                                        </td>


                                                        <?PAY_MASTER?>

                                                      </tr>
                                                    </table>
                                                  </td>
                                                </tr>
                                              </table>
                                            </template_line>
                                          </td>
                                        </tr>
                                      </table>
                                    </td>
                                    <td align="left">
                                      <table width=540 height=515 bgcolor="000000"
                                        background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
                                        <tr>
                                          <td height=0></td>
                                        </tr>
                                        <tr>
                                          <td width=1></td>
                                          <td align="center">
                                            <table cellspacing=0 cellpadding=0>
                                              <tr>
                                                <td align="center">
                                                  <img src="L2UI.SquareGray" width=520 height=1>
                                                  <table width=520 bgcolor=333333>
                                                    <tr>
                                                      <td align=center width=520>
                                                        <font color="LEVEL">辅助技能%skillName%的设定</font>
                                                      </td>
                                                    </tr>
                                                  </table>
                                                  <img src="L2UI.SquareGray" width=520 height=1><br>
                                                  <table cellspacing=0 cellpadding=0 border="1">
                                                      <tr>
                                                        <td width=60>
                                                          <font color=FFFF00>全部职业</font>
                                                        </td>
                                                        <td width=260 align="center">技能信息</td>
                                                        <td>
                                                          <font color=00FF00><button value="启用"
                                                              action="bypass -h htmbypass_bot.setbuffTarget %skillId% ALL true"
                                                              width=50 height=20 back="L2UI_CT1.Button_DF_Down"
                                                              fore="L2UI_CT1.Button_DF"></font>
                                                        </td>
                                                        <td>
                                                          <font color=FF0000><button value="关闭"
                                                              action="bypass -h htmbypass_bot.setbuffTarget %skillId% ALL false"
                                                              width=50 height=20 back="L2UI_CT1.Button_DF_Down"
                                                              fore="L2UI_CT1.Button_DF"></font>
                                                        </td>
                                                      </tr>
                                                      <tr>
                                                        <td>骑士职业</td>
                                                        <td>%KNIGHT%</td>
                                                        <td><button value="启用"
                                                            action="bypass -h htmbypass_bot.setbuffTarget %skillId% KNIGHT true"
                                                            width=50 height=20 back="Button_DF_Down" fore="Button_DF">
                                                        </td>
                                                        <td><button value="关闭"
                                                            action="bypass -h htmbypass_bot.setbuffTarget %skillId% KNIGHT false"
                                                            width=50 height=20 back="Button_DF_Down" fore="Button_DF">
                                                        </td>
                                                      </tr>
                                                      <tr>
                                                        <td>斗士职业</td>
                                                        <td>%WARRIOR%</td>
                                                        <td><button value="启用"
                                                            action="bypass -h htmbypass_bot.setbuffTarget %skillId% WARRIOR true"
                                                            width=50 height=20 back="Button_DF_Down" fore="Button_DF">
                                                        </td>
                                                        <td><button value="关闭"
                                                            action="bypass -h htmbypass_bot.setbuffTarget %skillId% WARRIOR false"
                                                            width=50 height=20 back="Button_DF_Down" fore="Button_DF">
                                                        </td>
                                                      </tr>
                                                      <tr>
                                                        <td>盗贼职业</td>
                                                        <td>%ROGUE%</td>
                                                        <td><button value="启用"
                                                            action="bypass -h htmbypass_bot.setbuffTarget %skillId% ROGUE true"
                                                            width=50 height=20 back="Button_DF_Down" fore="Button_DF">
                                                        </td>
                                                        <td><button value="关闭"
                                                            action="bypass -h htmbypass_bot.setbuffTarget %skillId% ROGUE false"
                                                            width=50 height=20 back="Button_DF_Down" fore="Button_DF">
                                                        </td>
                                                      </tr>
                                                      <tr>
                                                        <td>弓箭职业</td>
                                                        <td>%ARCHER%</td>
                                                        <td><button value="启用"
                                                            action="bypass -h htmbypass_bot.setbuffTarget %skillId% ARCHER true"
                                                            width=50 height=20 back="Button_DF_Down" fore="Button_DF">
                                                        </td>
                                                        <td><button value="关闭"
                                                            action="bypass -h htmbypass_bot.setbuffTarget %skillId% ARCHER false"
                                                            width=50 height=20 back="Button_DF_Down" fore="Button_DF">
                                                        </td>
                                                      </tr>
                                                    </table>
                                                    <table>
                                                      <tr>
                                                      <td width=250 height=30 align="center"><button value="返回" action="bypass -h htmbypass_bot.buff show" width=100 height=30 back="L2UI_CT1.Button_DF_Down"
                                                        fore="L2UI_CT1.Button_DF"></td>
                                                      <td width=250 align="center"><button value="帮助" action="bypass -h htmbypass_bot.help 14" width=100 height=30 back="Button_DF_Down" fore="Button_DF">
                                                            </td>
                                                      </tr>
                                                    </table>
                                                      <table width=490 align="center">
                                                        <tr>
                                                          <td align="center"><img src="L2UI_CH3.herotower_deco"
                                                              width=256 height=33></img></td>
                                                        </tr>
                                                      </table>
                                                      <table width=490 align="center" align="center">
                                                        <tr>
                                                          <td align="center"><button value="更新设置"
                                                              action="bypass -h htmbypass_bot.page main" width=250
                                                              height=32 back="L2UI_CT1.Button_DF_Down"
                                                              fore="L2UI_CT1.Button_DF"></td>
                                                        </tr>
                                                      </table>
                                                      <table width=490 align="center">
                                                        <tr>
                                                          <td align="center"><img src="L2UI_CH3.herotower_deco"
                                                              width=256 height=33></img></td>
                                                        </tr>
                                                      </table>
                                                </td>
                                              </tr>
                                              <tr>
                                                <td height=26></td>
                                              </tr>
                                            </table>
                                          </td>
                                        </tr>
                                      </table>
                                      <table>
                                        <tr>
                                          <td height=4></td>
                                        </tr>
                                      </table>
                                    </td>
                                  </tr>
                                </table>
                              </td>
                            </tr>
                          </table>





</body>

</html>