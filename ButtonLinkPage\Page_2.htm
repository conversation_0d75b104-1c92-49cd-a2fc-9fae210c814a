<!-- 设置保存 -->
<html>

<head>
  <title>组队设置</title>
</head>

<body>
  <center>
    <!-- 第一部分：居中词条"组队设置" -->
    <table width="270" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td align="center">
          <font color="LEVEL">组队设置</font>
        </td>
      </tr>
    </table>
    <br>

    <!-- 第二部分：具体功能实现 -->
    <!-- 1行：当前接受队长的管理 -->
    <table width="270" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td align="center">当前接受{队长名称}的管理</td>
      </tr>
    </table>

    <!-- 2行：绿色提示文字 -->
    <table width="270" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td align="center">
          <font color="00FF00">队伍分配请在组队前设定好</font>
        </td>
      </tr>
    </table>

        <!-- 3行：输入名字，添加队员，指定队长 -->
        <table width="270" border="0" cellspacing="0" cellpadding="3">
          <tr>
            <td width="60">输入名字:</td>
            <td width="80">
              <edit var="inputname" width="80" height="13">
            </td>
            <td width="65"><button value="添加队员" action="" width="65" height="20" back="L2UI_CT1.Button_DF_Down"
                fore="L2UI_CT1.Button_DF"></td>
            <td width="65"><button value="指定队长" action="" width="65" height="20" back="L2UI_CT1.Button_DF_Down"
                fore="L2UI_CT1.Button_DF"></td>
          </tr>
        </table>
        
        <!-- 4行：下拉选择，添加队员，指定队长 -->
        <table width="270" border="0" cellspacing="0" cellpadding="3">
          <tr>
            <td width="60">下拉选择:</td>
            <td width="80">
              <combobox var="charselect" list="角色1;角色2;角色3;角色4" width="80" height="14">
            </td>
            <td width="65"><button value="添加队员" action="" width="65" height="20" back="L2UI_CT1.Button_DF_Down"
                fore="L2UI_CT1.Button_DF"></td>
            <td width="65"><button value="指定队长" action="" width="65" height="20" back="L2UI_CT1.Button_DF_Down"
                fore="L2UI_CT1.Button_DF"></td>
          </tr>
        </table>
        
        <!-- 5行：队伍分配，设定分配方式，清除分配 -->
        <table width="270" border="0" cellspacing="0" cellpadding="3">
          <tr>
            <td width="60">队伍分配:</td>
            <td width="80">
              <combobox var="distribution" list="指定;随机" width="80" height="14">
            </td>
            <td width="65"><button value="设定分配方式" action="" width="65" height="20" back="L2UI_CT1.Button_DF_Down"
                fore="L2UI_CT1.Button_DF"></td>
            <td width="65"><button value="清除分配" action="" width="65" height="20" back="L2UI_CT1.Button_DF_Down"
                fore="L2UI_CT1.Button_DF"></td>
          </tr>
        </table>
        
        <!-- 6行：全部启动，全部停止 -->
        <table width="270" border="0" cellspacing="0" cellpadding="3">
          <tr>
            <td align="center">
              <button value="全部启动" action="" width="120" height="25" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
            </td>
            <td align="center">
              <button value="全部停止" action="" width="120" height="25" back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
            </td>
          </tr>
        </table>


    <!-- 返回主页按钮 -->
    <br>
    <table width=270 cellspacing=0 cellpadding=0>
      <tr>
        <td width=90 align=center>
          <button value="返回" action="link built_in_bot_system.htm" width=80 height=20 back="L2UI_CT1.Button_DF_Down"
            fore="L2UI_CT1.Button_DF">
        </td>
        <td width=90 align=center>
          <button value="应用设置" action="" width=80 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
        </td>
        <td width=90 align=center>
          <button value="重置" action="" width=80 height=20 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
        </td>
      </tr>
    </table>
  </center>
</body>

</html>