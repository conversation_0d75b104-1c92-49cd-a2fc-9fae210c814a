<!DOCTYPE html>
<html>

<head>
  <title>内挂初始界面</title>
</head>

<body>
  <center>
    <!-- 第一部分：居中词条"内挂初始界面" -->
    <table width="270" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td align="center">
          <font color="LEVEL">内挂初始界面</font>
        </td>
      </tr>
    </table>
    <br>

    <!-- 第二部分：具体功能实现 -->
    <!-- 2.1 靠左词条"职业选择" -->
    <table width="270" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <td align="left">
          <font color="00FFFF">职业选择</font>
        </td>
      </tr>
    </table>
    <img src="L2UI.SquareGray" width=270 height=1>
    <table width=270 bgcolor=111111>
      <tr>
        <td width=32 valign=top><img src="icon.skill0925" width=32 height=32></td>
        <td width=100><button value="一键法师" action="bypass _bbs_buffer_um?name=mage &pre=1 &target=0  &zone=1" width=100
            height=32 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
        <td width=100><button value="一键战士" action="bypass _bbs_buffer_um?name=warrior &pre=1 &target=0 &zone=1"
            width=100 height=32 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
        <td width=32 valign=top><img src="icon.skill0920" width=32 height=32></td>
      </tr>
    </table>
    <br1>
      <img src="L2UI.SquareGray" width=270 height=1>
      <table width=270 bgcolor=111111>
        <tr>
          <td width=32 valign=top><img src="icon.skill0929" width=32 height=32></td>
          <td width=100><button value="一键法师&宠物" action="bypass _bbs_buffer_um?name=mage &pre=1 &target=Pet &zone=1"
              width=100 height=32 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
          <td width=100><button value="一键战士&宠物" action="bypass _bbs_buffer_um?name=warrior &pre=1 &target=Pet &zone=1"
              width=100 height=32 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
          <td width=32 valign=top><img src="icon.skill0930" width=32 height=32></td>
        </tr>
      </table>
      <br1>

        <!-- 2.2 靠左词条"组队设置" -->
        <table width="270" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="left">
              <font color="00FFFF">组队设置</font>
            </td>
          </tr>
        </table>
        <img src="L2UI.SquareGray" width=270 height=1>
        <table width=270 bgcolor=111111>
          <tr>
            <td align="center">
              <button value="组队设置" action="" width=100 height=32 back="L2UI_CT1.Button_DF_Down"
                fore="L2UI_CT1.Button_DF">
            </td>
          </tr>
        </table>
        <br1>

          <!-- 2.3 靠左词条"详细设置" -->
          <table width="270" border="0" cellspacing="0" cellpadding="0">
            <tr>
              <td align="left">
                <font color="00FFFF">详细设置</font>
              </td>
            </tr>
          </table>
          <img src="L2UI.SquareGray" width=270 height=1>
          <table width=270 bgcolor=111111>
            <tr>
              <td align="center">
                <button value="详细设置" action="bypass -h _bbs_open?file=bot\main" width=100 height=32
                  back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
              </td>
            </tr>
          </table>
          <br1>

            <!-- 2.4 靠左词条"设置保存" -->
            <table width="270" border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td align="left">
                  <font color="00FFFF">设置保存</font>
                </td>
              </tr>
            </table>
            <img src="L2UI.SquareGray" width=270 height=1>
            <!-- 1行：历史配置方案 -->
            <table width=270 bgcolor=1F1818 cellspacing=0 cellpadding=0>
              <tr>
                <td width=60>
                  <font color="00FF00">历史方案</font>
                </td>
                <td width=100>
                  <combobox width=110 var=historyplan list="方案1;方案2;方案3;方案4">
                </td>
                <td width=90 align=center>
                  <button value="配置为当前方案" action="" width=100 height=20 back="L2UI_CT1.Button_DF_Down"
                    fore="L2UI_CT1.Button_DF">
                </td>
              </tr>
            </table>

            <!-- 2行：当前配置方案 -->
            <table width=270 bgcolor=1F1818 cellspacing=0 cellpadding=0>
              <tr>
                <td width=60>
                  <font color="00FF00">当前方案</font>
                </td>
                <td width=100>
                  <edit var=currentplan width=110 height=13>
                </td>
                <td width=90 align=center>
                  <button value="保存到历史方案" action="" width=100 height=20 back="L2UI_CT1.Button_DF_Down"
                    fore="L2UI_CT1.Button_DF">
                </td>
              </tr>
            </table>

            <!-- 3行：为同队伍的队友推荐当前方案 -->
            <table width=270 bgcolor=1F1818 cellspacing=0 cellpadding=0>
              <tr>
                <td width=120>
                  <font color="FFFFFF">为同队伍的队友推荐当前方案</font>
                </td>
                <td width=60>
                  <combobox width=70 var=teammate list="张三;李四;王五">
                </td>
                <td width=70 align=center>
                  <button value="推荐" action="" width=50 height=20 back="L2UI_CT1.Button_DF_Down"
                    fore="L2UI_CT1.Button_DF">
                </td>
              </tr>
            </table>
            <br1>

              <!-- 2.5 靠左词条"恢复设置" -->
              <table width="270" border="0" cellspacing="0" cellpadding="0">
                <tr>
                  <td align="left">
                    <font color="00FFFF">恢复设置</font>
                  </td>
                </tr>
              </table>
              <img src="L2UI.SquareGray" width=270 height=1>
              <table width=270 bgcolor=111111>
                <tr>
                  <td align="center">
                    <button value="恢复设置" action="" width=100 height=32 back="L2UI_CT1.Button_DF_Down"
                      fore="L2UI_CT1.Button_DF">
                  </td>
                </tr>
              </table>
              <br1>

                <!-- 2.6 靠左词条"组队回城" -->
                <table width="270" border="0" cellspacing="0" cellpadding="0">
                  <tr>
                    <td align="left">
                      <font color="00FFFF">组队回城</font>
                    </td>
                  </tr>
                </table>
                <img src="L2UI.SquareGray" width=270 height=1>
                <table width=270 bgcolor=111111>
                  <tr>
                    <td align="center">
                      <button value="组队回城" action="" width=100 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                  </tr>
                </table>
                <br>

                <!-- 第三部分：左右两个按钮 -->
                <table width="270" border="0" cellspacing="0" cellpadding="0">
                  <tr>
                    <td width="135" align="center">
                      <button value="开启" action="" width=100 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                    <td width="135" align="center">
                      <button value="取消" action="" width=100 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                  </tr>
                </table>
  </center>
</body>

</html>