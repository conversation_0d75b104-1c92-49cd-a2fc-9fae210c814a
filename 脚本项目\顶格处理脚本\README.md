# HTML顶格处理脚本

## 功能说明
这个Java脚本用于自动处理HTML文件的格式，主要功能包括：

1. **自动搜索htm文件**：在指定目录下搜索唯一的htm或html文件
2. **错误检查**：如果找不到htm文件或找到多个htm文件，会报错并停止运行
3. **顶格处理**：删除每行开头的空格和制表符，让代码顶格显示
4. **删除空行**：删除只包含空格和制表符的纯空行
5. **原地修改**：直接在原文件基础上进行修改

## 使用方法

### 方法1：使用批处理文件（推荐）
1. 将脚本文件复制到包含htm文件的目录中
2. 双击运行 `run.bat`
3. 脚本会自动处理当前目录下的htm文件

### 方法2：命令行运行
```bash
# 编译Java文件
javac -encoding UTF-8 HtmlFormatter.java

# 运行脚本（处理当前目录）
java HtmlFormatter

# 运行脚本（处理指定目录）
java HtmlFormatter "目标目录路径"
```

## 处理逻辑
1. **逐行扫描**：程序会逐行读取htm文件内容
2. **删除前导空白**：对于每一行，删除第一个非空格且非制表符字符之前的所有空格和制表符
3. **删除空行**：如果某行只包含空格和制表符，则完全删除该行
4. **保存修改**：处理完成后直接覆盖原文件

## 错误处理
- 如果目录中没有htm文件：显示错误信息并退出
- 如果目录中有多个htm文件：显示所有找到的文件名并退出
- 如果文件读写过程中出错：显示详细错误信息

## 输出信息
脚本运行时会显示：
- 搜索的目录路径
- 找到的目标文件名
- 删除的空行信息（行号和内容）
- 处理统计（原始行数、删除空行数、最终行数）

## 注意事项
- 请确保在运行脚本前备份重要文件
- 脚本会直接修改原文件，无法撤销
- 确保Java环境已正确安装并配置
