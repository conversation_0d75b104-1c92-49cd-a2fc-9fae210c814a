---
description: 
globs: 
alwaysApply: true
---
注意：
1.如果是修改原有的文件,不要主动闭合文件末尾的几行的标签，那里的标签是刻意不闭合的。
2.未指明图片时，统一使用<img src="icon.skill0831" width=32 height=32>
3.代码样式示例：
  3.1 复选框 参考 htm示例文件\样式代码库\复选框.txt
  3.2 下拉框 参考 htm示例文件\样式代码库\下拉框.txt
  3.3 输入框 参考 htm示例文件\样式代码库\输入框.txt
  3.4 分割线 参考 htm示例文件\样式代码库\分割线.txt
4. 所有的Htm里面的语句:1.文字必须使用中文 2.标点符号只能用英文标点，而非中文标点
5. 所有的后端交互逻辑都不用注意，例如：
  5.1 变量可以写死为具体的数值，如：8888；
  5.2 任何标签里面的action属性值必须空着，只有当用户明确要求你写action属性值时，你才写；
6.文字颜色：
  6.1 绿色：<font color="00FF00">自动找怪</font>
  6.2 红色：<font color="FF0000">自动回收</font>
  6.3 黄色：<font color="ADFF2F">建议设置队长保护</font>
7.文字大小：
  7.1 默认文字大小就是：15px，在定义表格的td的大小的时候，可以用这个值计算

