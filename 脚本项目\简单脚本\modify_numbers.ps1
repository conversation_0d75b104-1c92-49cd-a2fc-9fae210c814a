# PowerShell脚本：将a.txt文件中每行最后的数字改为原来的30%

$inputFile = "a.txt"
$outputFile = "a_modified_ps.txt"

# 检查输入文件是否存在
if (-not (Test-Path $inputFile)) {
    Write-Host "文件 $inputFile 不存在！" -ForegroundColor Red
    exit 1
}

# 读取文件内容
$content = Get-Content $inputFile -Raw

# 使用正则表达式匹配并替换数字
$pattern = '(\[adena\];)(\d+)(}}[;}]?)'

$modifiedContent = [regex]::Replace($content, $pattern, {
    param($match)

    $prefix = $match.Groups[1].Value      # [adena];
    $numberStr = $match.Groups[2].Value   # 原数字
    $suffix = $match.Groups[3].Value      # 后缀部分

    # 计算30%
    $originalNumber = [long]$numberStr
    $newNumber = [Math]::Round($originalNumber * 0.3)

    # 返回替换后的字符串
    return $prefix + $newNumber + $suffix
})

# 写入新文件
$modifiedContent | Out-File -FilePath $outputFile -Encoding UTF8 -NoNewline

Write-Host "处理完成！结果已保存到 $outputFile" -ForegroundColor Green
Write-Host "修改的数字统计：" -ForegroundColor Yellow

# 显示修改统计
$lines = $modifiedContent -split "`n"
for ($i = 0; $i -lt $lines.Length; $i++) {
    if ($lines[$i] -match '\[adena\];(\d+)') {
        $lineNumber = $i + 1
        $newValue = $matches[1]
        Write-Host "第${lineNumber}行: $newValue" -ForegroundColor Cyan
    }
}
