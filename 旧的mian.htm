<html>
<title>基本设置</title>

<body>
  <table>
    <tr>
      <td height=0></td>
    </tr>
    <tr>
      <td align="left">
        <table width=884>
          <tr>
            <td align="left">
              <table width=884 height=42 background="Castle_community.france_bg_community_menu">
                <tr>
                  <td align="center"><button value=" " action="bypass -h _bbs_open?file=board\index" width=122 height=40
                      back="Castle_community.btn_home_down" fore="Castle_community.btn_home"></td>
                  <td align="center"><button value="会员" action="bypass -h _bbs_open?file=board\service" width=122
                      height=40 back="Castle_community.btn_menu_down" fore="Castle_community.btn_menu"></td>
                  <td align="center"><button value="物品" action="bypass -h _bbs_open?file=board\adena" width=122
                      height=40 back="Castle_community.btn_menu_down" fore="Castle_community.btn_menu"></td>
                  <td align="center"><button value="坐骑" action="bypass -h _bbs_open?file=board\mount" width=122
                      height=40 back="Castle_community.btn_menu_down" fore="Castle_community.btn_menu"></td>
                  <td align="center"><button value="宠物" action="bypass -h _bbs_open?file=board\pet_manager" width=122
                      height=40 back="Castle_community.btn_menu_down" fore="Castle_community.btn_menu"></td>
                  <td align="center"><button value="时装" action="bypass -h _bbs_open?file=board\accessories" width=122
                      height=40 back="Castle_community.btn_menu_down" fore="Castle_community.btn_menu_over"></td>
                  <td align="center"><button value="服务" action="bypass -h _bbs_open?file=board\clan" width=122 height=40
                      back="Castle_community.btn_menu_down" fore="Castle_community.btn_menu"></td>
                </tr>
              </table>
            </td>
          </tr>
        </table>

        <table height=662 width=900 bgcolor="000000" background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
          <tr>
            <td>

              <table width=884>
                <tr>
                  <td height=0></td>
                </tr>
                <tr>
                  <td width=0></td>
                  <td align="left"><img src="Castle_community.bg" width="880" height="154"></td>
                </tr>
              </table>

              <table>
                <tr>
                  <td align="left">
                    <table width=470>
                      <tr>
                        <td height=6></td>
                      </tr>
                      <tr>
                      </tr>
                      <tr>
                        <td align="left">
                          <table width=470 height=100 bgcolor="000000" background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
                            <tr>
                              <td height=0></td>
                            </tr>
                            <tr>
                              <td width=1></td>
                              <td align="left">
                                <table>
                                  <tr>

                                    <table width=900 cellspacing=0>
                                      <tr>
                                        <td><button value="基本设置" action="link bot/main.htm" width=213
                                            height=32 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                                        <td><button value="技能策略" action="link bot/fight.htm" width=213
                                            height=32 back="Button_DF_Down" fore="Button_DF"></td>
                                        <td><button value="打怪范围" action="link bot/path.htm" width=213
                                            height=32 back="Button_DF_Down" fore="Button_DF"></td>
                                        <td><button value="加血加蓝" action="link bot/protect.htm" width=213
                                            height=32 back="Button_DF_Down" fore="Button_DF"></td>
                                      </tr>
                                      <tr>
                                        <td>
                                          <font color="00FF00"><button value="状态魔法"
                                              action="link bot/buff.htm" width=213 height=32
                                              back="Button_DF_Down" fore="Button_DF"></font>
                                        </td>
                                        <td>
                                          <font color="FFFF00"><button value="状态道具"
                                              action="link bot/itemuse.htm" width=213 height=32
                                              back="Button_DF_Down" fore="Button_DF"></font>
                                        </td>
                                        <td>
                                          <font color="C0FF3E"><button value="休息设定"
                                              action="link bot/rest.htm" width=213 height=32
                                              back="Button_DF_Down" fore="Button_DF"></font>
                                        </td>
                                        <td>
                                          <font color="9A32CD"><button value="召唤设定"
                                              action="link bot/pet.htm" width=213 height=32
                                              back="Button_DF_Down" fore="Button_DF"></font>
                                        </td>
                                      </tr>
                                    </table>

                                  </tr>
                                  <tr>
                                    <td height=26></td>
                                  </tr>
                                </table>
                              </td>
                            </tr>
                          </table>

                          <table>
                            <tr>

                              <td align="left">
                                <table width=120>
                                  <tr>
                                    <td height=6></td>
                                  </tr>
                                  <tr>

                                    <td align="left">
                                      <table width=200 height=370 bgcolor="000000"
                                        background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
                                        <tr>
                                          <td height=16></td>
                                        </tr>
                                        <tr>
                                          <td>

                                            <template_line>
                                              <table <?PAY_COLOR?> >
                                                <tr>
                                                  <td>
                                                    <table>
                                                      <tr>
                                                        <td align="left" width=16>
                                                          <img src="<?PAY_DISPLAY?>" width=16 height=16>
                                                        </td>

                                                        <td align="left" width=32>
                                                          <img src="<?PAY_CLASS_OIC?>" width="32" height="32">
                                                        </td>

                                                        <td align="left" width=160>
                                                          <button align="left" value="<?PAY_NAME?> - LV:<?PAY_LV?>"
                                                            action="bypass get_pay_setting?id=<?PAY_ID?>" width=135
                                                            height=32
                                                            back="L2UI_NewTex.ItemEnchantWnd.SwitchTab_Blue_Down"
                                                            fore="L2UI_NewTex.ItemEnchantWnd.SwitchTab_Blue_Over">
                                                        </td>


                                                        <?PAY_MASTER?>

                                                      </tr>
                                                    </table>
                                                  </td>
                                                </tr>
                                              </table>
                                            </template_line>



                                          </td>
                                        </tr>
                                      </table>
                                    </td>

                                    <td align="left">
                                      <table width=650 height=370 bgcolor="000000"
                                        background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
                                        <tr>
                                          <td height=0></td>
                                        </tr>
                                        <tr>
                                          <td width=1></td>
                                          <td align="left">
                                            <table>
                                              <tr>
                                                <td>





                                                <td>

                                                  <table>
                                                    <tr>
                                                      <td background="L2UI.CheckBox" width=14 height=16><button
                                                          value=" "
                                                          action="bypass -h htmbypass_bot.configSet autoAttack" width=15
                                                          height=14 back="" fore=""></td>
                                                      <td fixwidth=54>
                                                        <font color="00FF00">自动找怪</font>
                                                      </td>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" " action="bypass -h htmbypass_bot.configSet autoSweep"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>
                                                        <font color=FF0000>自动回收</font>
                                                      </td>
                                                    </tr>
                                                    <tr>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" "
                                                          action="bypass -h htmbypass_bot.configSet absorbBody" width=15
                                                          height=14 back="" fore=""></td>
                                                      <td fixwidth=54>死体吸血</td>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" " action="bypass -h htmbypass_bot.configSet acceptRes"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>接受复活</td>
                                                    </tr>
                                                    <tr>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" "
                                                          action="bypass -h htmbypass_bot.configSet usePhysicalAttack"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>使用平砍</td>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" "
                                                          action="bypass -h htmbypass_bot.configSet followMove" width=15
                                                          height=14 back="" fore=""></td>
                                                      <td fixwidth=54>跟随移动</td>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" "
                                                          action="bypass -h htmbypass_bot.configSet followAttack"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>跟随战斗</td>
                                                    </tr>
                                                    <tr>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" " action="bypass -h htmbypass_bot.configSet antidote"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>自我解毒</td>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" " action="bypass -h htmbypass_bot.configSet bondage"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>自我止血</td>
                                                    </tr>
                                                    <tr>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" "
                                                          action="bypass -h htmbypass_bot.configSet partyAntidote"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>队伍解毒</td>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" "
                                                          action="bypass -h htmbypass_bot.configSet partyBondage"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>队伍止血</td>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" "
                                                          action="bypass -h htmbypass_bot.configSet partyParalysis"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>队解麻痹</td>
                                                    </tr>
                                                  </table>
                                                  <br>
                                                  <table>
                                                    <tr>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=16><button
                                                          value=" "
                                                          action="bypass -h htmbypass_bot.configSet coverMember"
                                                          width=15 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>保护队友</td>
                                                      <td fixwidth=154>
                                                        <font color=ADFF2F>建议设置队长保护</font>
                                                      </td>
                                                    </tr>
                                                  </table>


                                                  <table align="center">
                                                    <tr>
                                                      <td background="L2UI.CheckBox_checked" width=14 height=14><button
                                                          value=" " action="bypass -h htmbypass_bot.configSet hpmpShift"
                                                          width=14 height=14 back="" fore=""></td>
                                                      <td fixwidth=54>心灵转换</td>
                                                      <td width=60>HP>:20%时</td>
                                                      <td>
                                                        <combobox var="hmsp" list="30;40;50;60;20;10;5" width=40
                                                          height=14>
                                                      </td>
                                                      <td><button value="设置"
                                                          action="bypass -h htmbypass_bot.configSet hpmpshiftpercent $hmsp"
                                                          width=46 height=14 back="L2UI_CT1.Button_DF_Down"
                                                          fore="L2UI_CT1.Button_DF"></td>
                                                    </tr>
                                                  </table>

                                                  <table width=630 align="center">
                                                    <tr>
                                                      <td align="center"><img src="L2UI_CH3.herotower_deco" width=256
                                                          height=33></img></td>
                                                    </tr>
                                                  </table>



                                                  <table width=630 align="center" align="center">
                                                    <tr>
                                                      <td align="center"><button value="更新设置"
                                                          action="bypass -h htmbypass_bot.page main" width=300 height=32
                                                          back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
                                                    </tr>
                                                  </table>

                                                  <table width=630 align="center">
                                                    <tr>
                                                      <td align="center"><img src="L2UI_CH3.herotower_deco" width=256
                                                          height=33></img></td>
                                                    </tr>
                                                  </table>
                                                </td>



                                              </tr>
                                              <tr>
                                                <td height=26></td>
                                              </tr>
                                            </table>
                                          </td>
                                        </tr>
                                      </table>

                                      <table>
                                        <tr>
                                          <td height=4></td>
                                        </tr>
                                      </table>


                                    </td>
                                  </tr>
                                </table>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>

</html>