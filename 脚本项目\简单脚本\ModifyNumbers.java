import java.io.*;
import java.nio.file.*;
import java.util.regex.*;

public class ModifyNumbers {
    public static void main(String[] args) {
        String inputFile = "a.txt";
        String outputFile = "a_modified.txt";
        
        try {
            // 读取文件内容
            String content = Files.readString(Paths.get(inputFile));
            
            // 使用正则表达式匹配并替换数字
            // 匹配模式：[adena];数字，可能后面跟着}};或}}
            Pattern pattern = Pattern.compile("(\\[adena\\];)(\\d+)(\\}\\}[;}]?)");
            Matcher matcher = pattern.matcher(content);
            
            StringBuffer result = new StringBuffer();
            while (matcher.find()) {
                String prefix = matcher.group(1);  // [adena];
                String numberStr = matcher.group(2);  // 原数字
                String suffix = matcher.group(3);  // 后缀部分
                
                // 计算30%
                long originalNumber = Long.parseLong(numberStr);
                long newNumber = Math.round(originalNumber * 0.3);
                
                // 替换
                matcher.appendReplacement(result, prefix + newNumber + suffix);
            }
            matcher.appendTail(result);
            
            // 写入新文件
            Files.writeString(Paths.get(outputFile), result.toString());
            
            System.out.println("处理完成！结果已保存到 " + outputFile);
            System.out.println("修改的行数统计：");
            
            // 显示修改统计
            String[] lines = result.toString().split("\n");
            for (int i = 0; i < lines.length; i++) {
                Pattern checkPattern = Pattern.compile("\\[adena\\];(\\d+)");
                Matcher checkMatcher = checkPattern.matcher(lines[i]);
                if (checkMatcher.find()) {
                    System.out.println("第" + (i+1) + "行: " + checkMatcher.group(1));
                }
            }
            
        } catch (IOException e) {
            System.err.println("文件操作错误: " + e.getMessage());
        } catch (NumberFormatException e) {
            System.err.println("数字格式错误: " + e.getMessage());
        }
    }
}
