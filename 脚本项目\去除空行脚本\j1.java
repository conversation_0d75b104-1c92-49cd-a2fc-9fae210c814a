import java.io.*;
import java.nio.file.*;
import java.util.List;
import java.util.stream.Collectors;

public class j1 {
    public static void main(String[] args) {
        // 检查命令行参数
        if (args.length != 1) {
            System.out.println("使用方法: java j1 <文件路径>");
            System.out.println("示例: java j1 test.txt");
            System.out.println("示例: java j1 C:\\path\\to\\your\\file.txt");
            System.out.println("示例: java j1 脚本项目\\去除空行脚本\\test.txt");
            return;
        }

        String filePath = args[0];

        try {
            // 处理路径，支持相对路径和绝对路径
            Path path = Paths.get(filePath);

            // 如果是相对路径，则相对于当前工作目录
            if (!path.isAbsolute()) {
                path = Paths.get(System.getProperty("user.dir"), filePath);
            }

            // 规范化路径
            path = path.normalize();

            System.out.println("处理文件: " + path.toString());

            if (!Files.exists(path)) {
                System.out.println("错误: 文件不存在 - " + path.toString());
                return;
            }
            
            // 读取文件所有行
            List<String> lines = Files.readAllLines(path);
            System.out.println("原文件共有 " + lines.size() + " 行");
            
            // 过滤掉空行（包括只包含空白字符的行）
            List<String> nonEmptyLines = lines.stream()
                .filter(line -> !line.trim().isEmpty())
                .collect(Collectors.toList());
            
            System.out.println("去除空行后共有 " + nonEmptyLines.size() + " 行");
            System.out.println("删除了 " + (lines.size() - nonEmptyLines.size()) + " 个空行");
            
            // 将处理后的内容写回文件
            Files.write(path, nonEmptyLines);
            
            System.out.println("文件处理完成: " + filePath);
            
        } catch (IOException e) {
            System.out.println("文件操作错误: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("程序执行错误: " + e.getMessage());
        }
    }
}
