import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class EnchantProcessor {
    
    public static void main(String[] args) {
        String inputFile = "C:\\Users\\<USER>\\Desktop\\Html目录_001\\苦力活\\test.txt";
        String outputFile = "C:\\Users\\<USER>\\Desktop\\Html目录_001\\苦力活\\result.txt";
        
        try {
            processEnchantData(inputFile, outputFile);
            System.out.println("处理完成！结果已保存到: " + outputFile);
        } catch (IOException e) {
            System.err.println("处理文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void processEnchantData(String inputFile, String outputFile) throws IOException {
        List<String> results = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(inputFile), StandardCharsets.UTF_8))) {
            
            String line;
            boolean isFirstLine = true;
            
            while ((line = reader.readLine()) != null) {
                // 跳过第一行（表头）
                if (isFirstLine) {
                    isFirstLine = false;
                    continue;
                }
                
                // 跳过空行或只包含制表符的行
                if (line.trim().isEmpty() || line.matches("^\\s*$")) {
                    continue;
                }
                
                // 分割数据行
                String[] columns = line.split("\t");
                
                // 检查是否有足够的列数据
                if (columns.length >= 7) {
                    String suitName = columns[0].trim();
                    String suitId = columns[1].trim();
                    String enchant6 = columns[2].trim();
                    String enchant7 = columns[3].trim();
                    String enchant8 = columns[4].trim();
                    String enchant9 = columns[5].trim();
                    String enchant10 = columns[6].trim();
                    
                    // 跳过套装ID为空的行
                    if (suitId.isEmpty()) {
                        continue;
                    }
                    
                    // 构建输出字符串
                    StringBuilder result = new StringBuilder();
                    result.append(suitId).append("---special_enchant_desc=[");
                    
                    // 处理强化+6
                    if (!enchant6.isEmpty()) {
                        result.append("强化+6\\n");
                        String[] parts6 = enchant6.split("，");
                        for (String part : parts6) {
                            if (!part.trim().isEmpty()) {
                                result.append(part.trim()).append("\\n");
                            }
                        }
                    }
                    
                    // 处理强化+7
                    if (!enchant7.isEmpty()) {
                        result.append("强化+7\\n");
                        String[] parts7 = enchant7.split("，");
                        for (String part : parts7) {
                            if (!part.trim().isEmpty()) {
                                result.append(part.trim()).append("\\n");
                            }
                        }
                    }
                    
                    // 处理强化+8
                    if (!enchant8.isEmpty()) {
                        result.append("强化+8\\n");
                        String[] parts8 = enchant8.split("，");
                        for (String part : parts8) {
                            if (!part.trim().isEmpty()) {
                                result.append(part.trim()).append("\\n");
                            }
                        }
                    }
                    
                    // 处理强化+9
                    if (!enchant9.isEmpty()) {
                        result.append("强化+9\\n");
                        String[] parts9 = enchant9.split("，");
                        for (String part : parts9) {
                            if (!part.trim().isEmpty()) {
                                result.append(part.trim()).append("\\n");
                            }
                        }
                    }
                    
                    // 处理强化+10
                    if (!enchant10.isEmpty()) {
                        result.append("强化+10\\n");
                        String[] parts10 = enchant10.split("，");
                        for (String part : parts10) {
                            if (!part.trim().isEmpty()) {
                                result.append(part.trim()).append("\\n");
                            }
                        }
                    }
                    
                    result.append("]");
                    results.add(result.toString());
                }
            }
        }
        
        // 写入结果文件
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(outputFile), StandardCharsets.UTF_8))) {
            
            for (String result : results) {
                writer.write(result);
                writer.newLine();
            }
        }
    }
}
