<html>
<title>自动 CP/HP/MP [设置]</title>
<body>
  <br>
  <center>
    <font name=hs28>自动设置</font>
    <img src="L2UI.SquareGray" width=285 height=1>
    <br>
    <font name=hs9 color=E3C855>自动功能<br>使用 CP/HP/MP 药剂</font>
    <br>
    <table cellspacing=0 cellpadding=0 border="1">
      <tr>
        <td width=15 height=40>
          <table>
            <tr>
              <td height="10"></td>
            </tr>
            <tr>
              <td>
                <button value=" " action="" width=15 height=15 back="L2UI.CheckBox_checked" fore="L2UI.CheckBox">
              </td>
            </tr>
          </table>
        </td>
        <td width=76>
          <font name=hs12 color=E3C855> 自动-CP</font>
        </td>
        <td width=76 align="center">
            <?user_cp_proc?>%
        </td>
        <td width=40>
          <table>
            <tr>
              <td height="6"></td>
            </tr>
            <tr>
              <td>
                <edit var="cp_per" type="number" min="0" max="99" value="10" width=40 height=14>
              </td>
            </tr>
          </table>
        </td>
        <td width=40>
          <table>
            <tr>
              <td height="4"></td>
            </tr>
            <tr>
              <td>
                <button action="bypass -h acp_cp_setting?proc=  $cp_per" value="设置" width=40 height=22
                  fore="L2UI_CT1.Button_DF_Small" back="L2UI_CT1.Button_DF_Small_Down">
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td width=15 height=40>
          <table>
            <tr>
              <td height="10"></td>
            </tr>
            <tr>
              <td>
                <button value=" " action="" width=15 height=15 back="L2UI.CheckBox_checked" fore="L2UI.CheckBox">
              </td>
            </tr>
          </table>
        </td>
        <td width=76>
          <font name=hs12 color=782736> 自动-HP</font>
        </td>
        <td width=76 align="center">
          <?user_hp_proc?>%
        </td>
        <td width=40>
          <table>
            <tr>
              <td height="6"></td>
            </tr>
            <tr>
              <td>
                <edit var="cp_per" type="number" min="0" max="99" value="10" width=40 height=14>
              </td>
            </tr>
          </table>
        </td>
        <td width=40>
          <table>
            <tr>
              <td height="4"></td>
            </tr>
            <tr>
              <td>
                <button action="bypass -h acp_cp_setting?proc=  $cp_per" value="设置" width=40 height=22
                  fore="L2UI_CT1.Button_DF_Small" back="L2UI_CT1.Button_DF_Small_Down">
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td width=15 height=40>
          <table>
            <tr>
              <td height="10"></td>
            </tr>
            <tr>
              <td>
                <button value=" " action="" width=15 height=15 back="L2UI.CheckBox_checked" fore="L2UI.CheckBox">
              </td>
            </tr>
          </table>
        </td>
        <td width=76>
          <font name=hs12 color=6585b6> 自动-MP</font>
        </td>
        <td width=76 align="center">
          <?user_mp_proc?>%
        </td>
        <td width=40>
          <table>
            <tr>
              <td height="6"></td>
            </tr>
            <tr>
              <td>
                <edit var="cp_per" type="number" min="0" max="99" value="10" width=40 height=14>
              </td>
            </tr>
          </table>
        </td>
        <td width=40>
          <table>
            <tr>
              <td height="4"></td>
            </tr>
            <tr>
              <td>
                <button action="bypass -h acp_cp_setting?proc=  $cp_per" value="设置" width=40 height=22
                  fore="L2UI_CT1.Button_DF_Small" back="L2UI_CT1.Button_DF_Small_Down">
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </center>
</body>
</html>
