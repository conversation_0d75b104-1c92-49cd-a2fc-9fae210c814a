# HTML界面开发教程

## 基础结构要求
- 文字必须使用中文，标点符号只能用英文标点
- 不可以有css和js(包括行内式的css,如：style=""),尽量参考教程和现成的htm文件,这里的htm文件比较特别

## 页面结构规范
- 顶部居中大标题，代码示例：
    <html noscrollbar>      
    <body>
      <center>    
        <table width=270>
          <tr>
            <td align=center width=270 valign=top>
              <font color="LEVEL">界面标题<br1>界面介绍</font>
            </td>
          </tr>
        </table>
- 变量可以写死为具体数值（如：8888）
- 所有标签的 action的具体值暂时不写


## 样式设计参考（参考样式）

- 包括：1.基础HTML结构，2.复选框，3.单选框/开关按钮，4.下拉框，5.表格布局，6.按钮组，7.分隔线，8.常用按钮样式，9.颜色方案，10.布局技巧

### 1. 基础HTML结构
```html
<html noscrollbar>
  <body>
    <center>
<!-- 页面内容 -->
    </center>
  </body>
</html>
```

### 2. 复选框设计
复选框设计方式
实现的效果是：一行里面可以有多个复选框结构

<table>
  <tr>
    <td background="L2UI.CheckBox_checked" width=14 height=16><button
        value=" "
        action=""
        width=15 height=14 back="" fore=""></td>
    <td fixwidth=54>队伍解毒</td>
    <td background="L2UI.CheckBox_checked" width=14 height=16><button
        value=" "
        action=""
        width=15 height=14 back="" fore=""></td>
    <td fixwidth=54>队伍止血</td>
    <td background="L2UI.CheckBox_checked" width=14 height=16><button
        value=" "
        action=""
        width=15 height=14 back="" fore=""></td>
    <td fixwidth=54>队解麻痹</td>
  </tr>
</table>

另一种复选框:
<button value=" " action="" width=15 height=15 back="L2UI.CheckBox_checked" fore="L2UI.CheckBox">

### 3. 单选框/开关按钮设计
```html
<!-- 开关按钮样式 - 15x15像素的小按钮 -->
<td width=20>
    <button action="动作" value=" " width=15 height=15
            back="图片资源" fore="图片资源">
</td>
```

### 4. 输入框设计
输入框使用edit标签
注意:一个字最少占有的宽度为: 字的个数乘以15
<table bgcolor=1F1818 cellspacing=0 cellpadding=0>
  <tr>
    <td width=60>
      <font color="00FF00">输入框内容介绍</font>
    </td>
    <td width=100>
      <edit var=currentplan width=110 height=13>
    </td>
  </tr>
</table>

### 4. 下拉框设计
下拉框的左边可能有词条，右边可能有按钮
示例代码为：
<table align="center">
  <tr>
    <!-- 介绍 -->
    <td width=60>当前的配置</td>
    <!-- 下拉框 -->
    <td>
      <combobox list="30;40;50;60;20;10;5" width=40 height=14>
    </td>
  </tr>
</table>

### 5. 表格布局设计
```html
<!-- 带背景色的表格 -->
<table width=298 bgcolor=1F1818>
    <tr>
        <td width=5></td>
        <td width=20><!-- 按钮区域 --></td>
        <td fixwidth=240><!-- 文字说明区域 --></td>
    </tr>
</table>

<!-- 状态显示表格 -->
<table width="295" cellpadding="0" bgcolor="452822">
    <tr>
        <td width="45" align="center">
            <img src="图标" width="32" height="32">
        </td>
        <td valign="top">
            <!-- 嵌套表格内容 -->
        </td>
    </tr>
</table>
```
表格设计时的特别注意:
1. 属性:valign=center 是没有效果的,并不能做到-上下居中
用户一般也用不到上下居中
2. 属性:align=center 是有效果的,可以做到-左右居中

### 6. 按钮组设计
```html
<!-- 水平按钮组 -->
<table width=270>
  <tr>
    <td width=135 align=center><button value="按钮名称" action="" height=32
        back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
    <td width=135 align=center><button value="按钮名称" action="" height=32
        back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
  </tr>
</table>
```

### 7. 分隔线设计
```html
<!-- 分隔线 -->
<img src="L2UI_CT1.Gauge_DF_CP_Center" width=298 height=2>

或者可以使用:
<img src="L2UI.SquareGray" width=270 height=1>
```

### 8. 常用按钮样式
- `back="L2UI_ct1.button_df" fore="L2UI_ct1.button_df"` - 标准按钮
- `back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"` - 大按钮
- `back="L2UI_CT1.ListCTRL_DF_Title_Down" fore="L2UI_CT1.ListCTRL_DF_Title"` - 标签页按钮
- `back="L2UI.CheckBox_checked" fore="L2UI.CheckBox"` - 复选框按钮
- `back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"` - 物品框按钮

### 9. 颜色方案
<font color="FFFFFF">白色的文字</font>
<font color="LEVEL">黄色的文字</font>
<font color="00FF00">绿色的文字</font>
<font color="b09979">橙色的文字</font>

### 10. 布局技巧
- 使用 `fixwidth` 属性固定列宽
- 使用嵌套表格实现复杂布局
- 使用 `cellspacing=0 cellpadding=0` 消除表格间距
- 使用 `valign` 和 `align` 控制对齐方式
- 使用 `width=5` 等小宽度td作为间距控制

### 11. 花样背景
  <table border="0" cellpadding="0" cellspacing="0" width="292"
    background="L2UI_CH3.refinewnd_back_Pattern">

- 利用 background="L2UI_CH3.refinewnd_back_Pattern" 达成花样的table背景

### 12. 界面小标题背景

<table width="270" bgcolor="333333"><!-- 333333是作为单行的小标题的颜色 -->
  <tr>
    <td width="270" align="center"><!-- 标题文字: color="LEVEL" (黄色高亮) -->
      <font color="LEVEL">界面小标题</font>
    </td>
  </tr>
</table>
<img src="L2UI.SquareGray" width=270 height=1>
注:使用bgcolor="1F1818"来作为正文标题:<table width="270" bgcolor="1F1818">

### 13. 顶部装饰
顶部的波浪线装饰
    <img src="L2UI_CH3.herotower_deco" width=256 height=30></img>

### 14. 水平居中技巧
align="center"
可以作用于 table 或者 td 的行内式属性

### 15. 大图片
大图片代码示例:
<table border=0 cellpadding=0 cellspacing=0>
  <tr>
    <td width=256 height=150 background="L2UI_OlympiadWnd_DF_GrandTexture"></td>
  </tr>
</table>

### 16. 高级的高度居中设计
```html
<table>
  <!-- 一行:下拉框，输入框 -->
  <tr>
    <td height=22 width=120>
      <table height=22 width=120>
        <tr>
          <td height=22 width=120>
            <font color=LEVEL>武器防具等级选择: </font>
          </td>
        </tr>
      </table>
    </td>
    <td height=22 width=60>
      <combobox list="武器;防具" width=50>
    </td>
    <td height=22 width=40>
      <combobox list="D;C;B;A;S;" width=32>
    </td>
    <td height=22 width=28></td>
    <td height=22 width=28>
      <table height=22 width=40>
        <tr>
          <td height=22 width=40>
            <font color=LEVEL>数量: </font>
          </td>
        </tr>
      </table>
    </td>
    <td height=22>
      <edit width=80>
    </td>
  </tr>
</table>
```
总结:
将所有的font标签 额外套一层高度为 22 的table,
实现在同一行中 font标签 与 输入框edit标签 和 下拉框combobox 在上下方向调和,达到高度居中设计

### 表格边框
<table cellspacing=0 cellpadding=0 border="1">

### 表格背景加黑
<table width=520 bgcolor=333333>