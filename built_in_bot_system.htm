<!-- 内挂系统的界面 -->
<!-- 入口文件，路由的根目录 -->
<html noscrollbar>

<body>
  <center>
    <!-- 三列布局显示数值：剩余积分、剩余内挂时间、剩余狩猎时间 -->
     <!-- 加入居中词条：内挂主界面 -->
    <br>
    <font color=LEVEL size=4><b>内挂主界面</b></font>
    <br><br>
    <table width=270 bgcolor=222222>
      <tr>
        <td width=90 align=center>
          <font color=LEVEL>剩余积分</font>
        </td>
        <td width=90 align=center>
          <font color=LEVEL>剩余内挂时间</font>
        </td>
        <td width=90 align=center>
          <font color=LEVEL>狩猎倒计时</font>
        </td>
      </tr>
      <tr>
        <td width=90 align=center>
          <font color=FFFFFF>9999</font>
        </td>
        <td width=90 align=center>
          <font color=FFFFFF>60分钟</font>
        </td>
        <td width=90 align=center>
          <font color=FFFFFF>120分钟</font>
        </td>
      </tr>
    </table>
    <table width=270 bgcolor=333333>
      <tr>
        <td align=center width=270>↓内挂功能配置区域↓</td>
      </tr>
    </table>
    <table fixwidth=270 fixheight=400 background="L2UI_CT1.Windows_DF_TooltipBG">
      <tr>
        <td fixwidth=270 fixheight=400 valign=top>
          <table fixwidth=270 fixheight=400>
            <tr>
              <td fixwidth=270 fixheight=400 valign=top>
                <!--
                    左右两排按钮布局，（2列*4行）一共八个按钮
                    职业选择，组队设置
                    详细设置，设置保存
                    恢复设置，组队回城
                    开启，取消
                -->
                <table width=270 cellspacing=5 cellpadding=5>
                  <!-- 第一行：职业选择，组队设置 -->
                  <tr>
                    <td width=135 align=center>
                      <button value="职业选择" action="link ButtonLinkPage/Page_1.htm" width=120 height=32
                        back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF">
                    </td>
                    <td width=135 align=center>
                      <button value="组队设置" action="link ButtonLinkPage/Page_2.htm" width=120 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                  </tr>
                  <!-- 第二行：详细设置，设置保存 -->
                  <tr>
                    <td width=135 align=center>
                      <button value="详细设置" action="bypass -h _bbs_open?file=bot\main" width=120 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                    <td width=135 align=center>
                      <button value="设置保存" action="link ButtonLinkPage/Page_4.htm" width=120 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                  </tr>
                  <!-- 第三行：恢复设置，组队回城 -->
                  <tr>
                    <td width=135 align=center>
                      <button value="恢复设置" action="link ButtonLinkPage/Page_5.htm" width=120 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                    <td width=135 align=center>
                      <button value="组队回城" action="link ButtonLinkPage/Page_6.htm" width=120 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                  </tr>
                  <!-- 第四行：开启，取消 -->
                  <tr>
                    <td width=135 align=center>
                      <button value="开启" action="" width=120 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                    <td width=135 align=center>
                      <button value="取消" action="" width=120 height=32 back="L2UI_CT1.Button_DF_Down"
                        fore="L2UI_CT1.Button_DF">
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </center>
</body>

</html>