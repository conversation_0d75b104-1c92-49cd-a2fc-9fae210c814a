<html>
<title>基本设置</title>
<body>
<table>
<tr>
<td height=0></td>
</tr>
<tr>
<td align="left">
<table height=662 width=760 bgcolor="000000" background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td>
<table>
<tr>
<td align="left">
<table width=470>
<tr>
<td height=6></td>
</tr>
<tr>
<td align="left">
<table width=470 height=100 bgcolor="000000" background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td height=0></td>
</tr>
<tr>
<td width=1></td>
<td align="left">
<table>
<tr>
<table width=750 cellspacing=0>
<tr>
<td><button value="基本设置"
action="bypass -h _bbs_aout_open?file=main & welfare=main" width=180
height=32 back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
<td><button value="技能策略"
action="bypass -h _bbs_aout_open?file=fight & welfare=fight" width=180
height=32 back="Button_DF_Down" fore="Button_DF"></td>
<td><button value="打怪范围"
action="bypass -h _bbs_aout_open?file=path & welfare=path" width=180
height=32 back="Button_DF_Down" fore="Button_DF"></td>
<td><button value="加血加蓝"
action="bypass -h _bbs_aout_open?file=protect & welfare=protect" width=180
height=32 back="Button_DF_Down" fore="Button_DF"></td>
</tr>
<tr>
<td>
<font color="00FF00"><button value="状态魔法"
action="bypass -h _bbs_aout_open?file=buff & welfare=buff" width=180
height=32 back="Button_DF_Down" fore="Button_DF"></font>
</td>
<td>
<font color="FFFF00"><button value="状态道具"
action="bypass -h _bbs_aout_open?file=itemuse & welfare=itemuse" width=180
height=32 back="Button_DF_Down" fore="Button_DF"></font>
</td>
<td>
<font color="C0FF3E"><button value="休息设定"
action="bypass -h _bbs_aout_open?file=rest & welfare=rest" width=180
height=32 back="Button_DF_Down" fore="Button_DF"></font>
</td>
<td>
<font color="9A32CD"><button value="召唤设定"
action="bypass -h _bbs_aout_open?file=pet & welfare=pet" width=180
height=32 back="Button_DF_Down" fore="Button_DF"></font>
</td>
</tr>
</table>
</tr>
<tr>
<td height=26></td>
</tr>
</table>
</td>
</tr>
</table>
<table>
<tr>
<td align="left">
<table width=120>
<tr>
<td height=6></td>
</tr>
<tr>
<td align="left">
<table width=200 height=515 bgcolor="000000"
background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td height=16></td>
</tr>
<tr>
<td>
                                            <template_line>
                                              <table <?PAY_COLOR?> >
                                                <tr>
                                                  <td>
                                                    <table>
                                                      <tr>
                                                        <td align="left" width=16>
                                                          <img src="<?PAY_CLASS_OIC?>" width=12 height=12>
                                                          <img src="<?PAY_DISPLAY?>" width=16 height=16>
                                                        </td>
                                                        <td align="left" width=150>
                                                          <button align="left" value="<?PAY_NAME?> - LV:<?PAY_LV?>"
                                                            action="bypass get_pay_setting?id=<?PAY_ID?>" width=150
                                                            height=32 back="L2UI_CT1.Button_DF_Down"
                                                            fore="L2UI_CT1.Button_DF">
                                                        </td>
                                                        <?PAY_MASTER?>
                                                      </tr>
                                                    </table>
                                                  </td>
                                                </tr>
                                              </table>
                                            </template_line>
</td>
</tr>
</table>
</td>
<td align="left">
<table width=540 height=515 bgcolor="000000"
background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=1></td>
<td align="left">
<template_lineEx>
<table>
<tr>
<td align="center">
<table align="center" border="1" bordercolor="CCCCCC" cellpadding="10"
cellspacing="0">
<tr>
<td>
<table align="center">
<tr>
<td width=15 height=20 align="center" valign="middle">
<button value=" " action="bypass -h htmbypass_bot.configSet hpmpShift" width=15 height=15 back= <?AUTO_FOLLOW?> fore= <?AUTO_FOLLOW?> >
</td>
<td fixwidth=36> 跟随:</td>
<td>
<combobox var="hmsp" list="张三;李四" width=70>
</td>
<td width=35> 距离:</td>
<td>
<combobox var="hmsp" list="20;50;80;100;200;300;5"
width=35>
</td>
<td width=10></td>
<td width=15 height=20 align="center" valign="middle">
<button value=" "
action="bypass -h htmbypass_bot.configSet followAttack"
width=15 height=15 back=<?AUTO_ATTACK?>
fore=<?AUTO_ATTACK?>>
</td>
<td fixwidth=55 align="center">选中攻击</td>
<td width=15 height=20 align="center" valign="middle">
<button value=" "
action="bypass -h htmbypass_bot.configSet followAttack"
width=15 height=15 back=<?AUTO_ATTACKEX?>
fore=<?AUTO_ATTACKEX?>>
</td>
<td fixwidth=55 align="center">跟随攻击</td>
<td><button value="设置"
action="bypass -h htmbypass_bot.configSet hpmpshiftpercent $hmsp"
width=70 height=20 back="L2UI_CT1.Button_DF_Down"
fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
</td>
</tr>
</table>
<br>
<table>
<tr>
<td background="L2UI.CheckBox" width=14 height=16><button
value=" "
action="bypass -h htmbypass_bot.configSet autoAttack" width=15
back="" fore=""></td>
<td fixwidth=54>
<font color="00FF00">自动找怪</font>
</td>
<td background="L2UI.CheckBox_checked" width=14 height=16>
<button value=" "
action="bypass -h htmbypass_bot.configSet autoSweep" width=15
back="" fore="">
</td>
<td fixwidth=54>
<font color=FF0000>自动回收</font>
</td>
<td background="L2UI.CheckBox_checked" width=14 height=16>
<button value=" "
action="bypass -h htmbypass_bot.configSet absorbBody" width=15
back="" fore="">
</td>
<td fixwidth=54>死体吸血</td>
</tr>
</table>
<br>
<table>
<tr>
<td background="L2UI.CheckBox_checked" width=14 height=16>
<button value=" "
action="bypass -h htmbypass_bot.configSet antidote" width=15
height=14 back="" fore="">
</td>
<td fixwidth=54>自我解毒</td>
<td background="L2UI.CheckBox_checked" width=14 height=16>
<button value=" "
action="bypass -h htmbypass_bot.configSet bondage" width=15
height=14 back="" fore="">
</td>
<td fixwidth=54>自我止血</td>
<td background="L2UI.CheckBox_checked" width=14 height=16>
<button value=" "
action="bypass -h htmbypass_bot.configSet acceptRes" width=15
height=14 back="" fore="">
</td>
<td fixwidth=54>接受复活</td>
</tr>
</table>
<br>
<table>
<tr>
<td background="L2UI.CheckBox_checked" width=14 height=16>
<button value=" "
action="bypass -h htmbypass_bot.configSet partyAntidote"
width=15 height=14 back="" fore="">
</td>
<td fixwidth=54>队伍解毒</td>
<td background="L2UI.CheckBox_checked" width=14 height=16>
<button value=" "
action="bypass -h htmbypass_bot.configSet partyBondage"
width=15 height=14 back="" fore="">
</td>
<td fixwidth=54>队伍止血</td>
<td background="L2UI.CheckBox_checked" width=14 height=16>
<button value=" "
action="bypass -h htmbypass_bot.configSet partyParalysis"
width=15 height=14 back="" fore="">
</td>
<td fixwidth=54>队解麻痹</td>
</tr>
</table>
<br>
<table align="center">
<tr>
<td width=15>
<button action="bypass -h htmbypass_bot.configSet hpmpShift"
value=" " width=15 height=15 back="L2UI.CheckBox_checked"
fore="L2UI.CheckBox">
</td>
<td fixwidth=54>心灵转换</td>
<td width=60>HP>:20%时</td>
<td>
<combobox var="hmsp" list="30;40;50;60;20;10;5" width=40>
</td>
<td><button value="设置"
action="bypass -h htmbypass_bot.configSet hpmpshiftpercent $hmsp"
width=46 height=20 back="L2UI_CT1.Button_DF_Down"
fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
<table width=490 align="center">
<tr>
<td align="center"><img src="L2UI_CH3.herotower_deco" width=256 height=33></img></td>
</tr>
</table>
<table width=490 align="center">
<tr>
<td align="center"><button value="保存设置"
action="bypass -h htmbypass_bot.page main" width=250 height=32
back="L2UI_CT1.Button_DF_Down" fore="L2UI_CT1.Button_DF"></td>
</tr>
</table>
<table width=490 align="center">
<tr>
<td align="center"><img src="L2UI_CH3.herotower_deco" width=256 height=33></img></td>
</tr>
</table>
</td>
</tr>
<tr>
<td height=26></td>
</tr>
</table>
</template_lineEx>
</td>
</tr>
</table>
<table>
<tr>
<td height=4></td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</body>
</html>
